using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.Players;
using Game.Views.PlayerUI;
using Game.Views.UI.Screens.Menu;
using Modules.Core;
using Modules.UI;
using VContainer;

namespace Game.Controllers.Screens
{
    public class SettingsPanelController : ControllerBase
    {
        private const float MinPlayerScale = 0.5f;
        private const float MaxPlayerScale = 5.0f;
        private const float ScaleIncrement = 0.5f;

        private MenuScreen menuScreen;
        private SettingsPanel settingsPanel;
        private PlayersModel playersModel;
        private HandsMenu handsMenu;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;
        
        private bool bubbleVisibility = true;

        [Inject]
        private void Construct(
            IScreenManager screenManager,
            PlayersModel playersModel,
            PlayerMenu playerMenu)
        {
            this.playersModel = playersModel;
            
            menuScreen = screenManager.GetScreen<MenuScreen>(true);
            settingsPanel = menuScreen.SettingsPanel;

            var menuPanel = menuScreen.GetPanel<MenuPanel>();
            menuPanel.OnOpenSettings.Subscribe(_ => HandleOpenSettings()).AddTo(DisposeCancellationToken);

            settingsPanel.OnScaleUp.Subscribe(_ => HandleScaleUp()).AddTo(DisposeCancellationToken);
            settingsPanel.OnScaleDown.Subscribe(_ => HandleScaleDown()).AddTo(DisposeCancellationToken);
            settingsPanel.OnToggleVisibility.Subscribe(_ =>
            {
                HandleToggleVisibility();
                HandleToggleBubbleVisibility();
            }).AddTo(DisposeCancellationToken);

            playersModel.LocalPlayer.Subscribe(HandleLocalPlayerChanged).AddTo(DisposeCancellationToken);
            
            handsMenu = playerMenu.HandsMenu;
        }

        private void HandleOpenSettings()
        {
            menuScreen.OpenPanel<SettingsPanel>();
            UpdateScaleButtons();
        }

        private void HandleScaleUp()
        {
            var currentScale = LocalPlayer.Scale.Value;
            var newScale = currentScale + ScaleIncrement;

            if (newScale > MaxPlayerScale)
            {
                return;
            }

            LocalPlayer.SetScale(newScale);
            UpdateScaleButtons();
        }

        private void HandleScaleDown()
        {
            var currentScale = LocalPlayer.Scale.Value;
            var newScale = currentScale - ScaleIncrement;

            if (newScale < MinPlayerScale)
            {
                return;
            }

            LocalPlayer.SetScale(newScale);
            UpdateScaleButtons();
        }
        
        private void HandleToggleVisibility()
        {
            if (LocalPlayer == null) return;

            LocalPlayer.VisibleNetworked = !LocalPlayer.VisibleNetworked;
        }
        
        private void HandleToggleBubbleVisibility()
        {
            handsMenu.SetInteractableWidgetNodeActive(!bubbleVisibility);
            handsMenu.SetHomeWidgetActive(!bubbleVisibility);
            handsMenu.SetInteractableWidgetActive(!bubbleVisibility);
            handsMenu.SetMoneyBagActive(!bubbleVisibility);
            bubbleVisibility = !bubbleVisibility;
        }

        private void HandleLocalPlayerChanged(PlayerActor player)
        {
            if (player != null)
            {
                player.Scale.Subscribe(_ => UpdateScaleButtons()).AddTo(player);
            }
        }

        private void UpdateScaleButtons()
        {
            if (LocalPlayer == null) return;

            var currentScale = LocalPlayer.Scale.Value;
            settingsPanel.SetActiveScaleUpButton(currentScale < MaxPlayerScale);
            settingsPanel.SetActiveScaleDownButton(currentScale > MinPlayerScale);
        }
    }
}
