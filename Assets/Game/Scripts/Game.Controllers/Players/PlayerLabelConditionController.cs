using System;
using System.Reactive;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Models;
using Modules.Core;
using VContainer;

namespace Game.Controllers.Players
{
    public class PlayerLabelConditionController : ControllerBase
    {
        private GameModel gameModel;
        private EconomyModel economyModel;

        [Inject]
        private void Construct(GameModel gameModel, EconomyModel economyModel)
        {
            this.gameModel = gameModel;
            this.economyModel = economyModel;

            gameModel.OnBeforeInitialized.Subscribe(HandleBeforeInitialized).AddTo(DisposeCancellationToken);
        }

        private void HandleBeforeInitialized(Unit unit)
        {
            foreach (var shopItem in economyModel.ShopItemList)
            {
                if (shopItem.HasViewCode(InventoryCodes.Panda))
                {
                    shopItem.SetPurchaseState(gameModel.IsPlayerModerator() || gameModel.IsPlayerTester());
                }
                else if (shopItem.HasViewCode(InventoryCodes.Polly))
                {
                    shopItem.SetPurchaseState(gameModel.IsPlayerModerator() || gameModel.IsContentCreator2());
                }
                else if (shopItem.HasViewCode(InventoryCodes.UBot))
                {
                    shopItem.SetPurchaseState(gameModel.IsPlayerModerator() || gameModel.IsContentCreator3());
                }
                else if (shopItem.HasViewCode(InventoryCodes.ModerationStick))
                {
                    shopItem.SetPurchaseState(gameModel.IsPlayerModerator() || gameModel.IsPlayerModeratorTrainee());
                }
                else if (shopItem.HasViewCode(InventoryCodes.ModAvatar))
                {
                    shopItem.SetPurchaseState(gameModel.IsPlayerModerator());
                }
                else if (shopItem.HasViewCode(InventoryCodes.ModSuit))
                {
                    shopItem.SetPurchaseState(gameModel.IsPlayerModerator());
                }
                else if (shopItem.HasViewCode(InventoryCodes.GoatMan))
                {
                    shopItem.SetPurchaseState(gameModel.IsPlayerModerator());
                }
                else if (shopItem.HasViewCode(InventoryCodes.Persuer))
                {
                    shopItem.SetPurchaseState(gameModel.IsPlayerModerator());
                }
            }
        }
    }
}