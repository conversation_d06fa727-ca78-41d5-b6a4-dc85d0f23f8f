using Cysharp.Threading.Tasks;
using Fusion;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        private readonly IAsyncReactiveProperty<bool> visible = new AsyncReactiveProperty<bool>(true);
        public IReadOnlyAsyncReactiveProperty<bool> Visible => visible;
        
        [Networked] [OnChangedRender(nameof(ChangeVisibleNetworked))]
        public bool VisibleNetworked { get; set; } = true;
        
        private void SetPlayerViewActive(bool isActive)
        {
            avatarProvider.AvatarView.SetActive(isActive);
            playerView.SetNameVisibleForOthers(isActive);
        }
        
        private void ChangeVisibleNetworked()
        {
            if (visible.Value != VisibleNetworked)
            {
                visible.Value = VisibleNetworked;
            }
            
            SetPlayerViewActive(visible.Value);
        }
    }
}
