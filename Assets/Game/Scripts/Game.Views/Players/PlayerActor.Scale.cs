using Cysharp.Threading.Tasks;
using Fusion;
using Modules.Core;
using UnityEngine;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        private readonly IAsyncReactiveProperty<float> scale = new AsyncReactiveProperty<float>(CoreConstants.WorldScale);
        public IReadOnlyAsyncReactiveProperty<float> Scale => scale;
        
        [Networked] [OnChangedRender(nameof(ChangeScaleNetworked))]
        private float ScaleNetworked { get; set; }
        
        public override void SetScale(float newScale)
        {
            ScaleNetworked = newScale;
        }
        
        private void ChangeScaleNetworked()
        {
            if (!Mathf.Approximately(scale.Value, ScaleNetworked))
            {
                scale.Value = ScaleNetworked;
            }

            transform.localScale = ScaleNetworked * Vector3.one;
        }
    }
}
