using System;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Modules.Core;
using Modules.UI;
using TMPro;
using UnityEngine;
using VContainer;

namespace Game.Views.Players
{
    public class PlayerView : Actor
    {
        [Header("Local Player")]
        [SerializeField] private GameObject localViewObject;
        [SerializeField] private GameObject localVoiceIndicatorObject;
        [SerializeField] private GameObject localVoiceOffIndicatorObject;
        [SerializeField] private GameObject localHealthObject;
        [SerializeField] private GameObject killedZombieCountObject;
        [SerializeField] private TMP_Text localHealthText;
        [SerializeField] private TMP_Text killedZombieCountText;
        [SerializeField] private GameObject raceObject;
        [SerializeField] private TMP_Text raceTimerText;
        [SerializeField] private TMP_Text raceCheckpointNumberText;

        [<PERSON><PERSON>("Remote Player")]
        [SerializeField] private GameObject remoteViewObject;
        [SerializeField] private GameObject remoteViewNode;
        [SerializeField] private GameObject remoteVoiceIndicatorObject;
        [SerializeField] private GameObject remoteVoiceOffIndicatorObject;
        [SerializeField] private GameObject remoteHealthObject;
        [SerializeField] private SliderWidget remoteHealthSlider;
        [SerializeField] private TMP_Text remotePlayerNameText;

        [Header("Shared")]
        [SerializeField] private Transform crownNode;
        [SerializeField] private GameObject crownAwardEffectObject;

        private IAudioClient audioClient;

        public Transform CrownNode => crownNode;

        [Inject]
        private void Construct(IAudioClient audioClient)
        {
            this.audioClient = audioClient;
        }

        public void SetActiveViewObject(bool isLocalPlayer)
        {
            localViewObject.SetActive(isLocalPlayer);
            remoteViewObject.SetActive(!isLocalPlayer);
        }
        
        public void SetNameVisibleForOthers(bool isVisible)
        {
            remotePlayerNameText.gameObject.SetActive(isVisible);
        }

        public void SetActiveVoiceIndicator(bool isLocalPlayer, bool isActive)
        {
            if (isLocalPlayer)
            {
                localVoiceIndicatorObject.SetActive(isActive);
            }
            else
            {
                remoteVoiceIndicatorObject.SetActive(isActive);
            }
        }

        public void SetActiveVoiceOffIndicator(bool isLocalPlayer, bool isActive)
        {
            if (isLocalPlayer)
            {
                localVoiceOffIndicatorObject.SetActive(isActive);
            }
            else
            {
                remoteVoiceOffIndicatorObject.SetActive(isActive);
            }
        }

        public void SetActiveHealthObject(bool isLocalPlayer, bool isActive)
        {
            if (isLocalPlayer)
            {
                localHealthObject.SetActive(isActive);
            }
            else
            {
                remoteHealthObject.SetActive(isActive);
            }
        }

        public void SetActiveKilledZombieCountObject(bool isActive)
        {
            killedZombieCountObject.SetActive(isActive);
        }

        public void SetPlayerName(bool isLocalPlayer, string message)
        {
            if (isLocalPlayer)
            {
                return;
            }

            remotePlayerNameText.text = message;
            remotePlayerNameText.gameObject.SetActive(true);
        }

        public void SetPlayerHealth(bool isLocalPlayer, float health, float maxHealth)
        {
            if (isLocalPlayer)
            {
                localHealthText.text = $"{health}";
            }
            else
            {
                remoteHealthSlider.SetValues(0, maxHealth, health, true, false);
            }
        }

        public void SetKilledZombieCount(float killedZombieCount)
        {
            killedZombieCountText.text = $"{killedZombieCount}";
        }

        public void SetViewHeight(bool isLocalPlayer, float height)
        {
            if (isLocalPlayer)
            {
                return;
            }

            remoteViewNode.transform.localPosition = remoteViewNode.transform.localPosition.SetY(height);
        }

        public void SetActiveRaceObject(bool isActive)
        {
            raceObject.SetActive(isActive);
        }

        public void SetRaceTimerText(string raceTimer)
        {
            raceTimerText.text = raceTimer;
        }

        public void SetCheckpointNumberText(string raceCheckpointNumber)
        {
            raceCheckpointNumberText.text = raceCheckpointNumber;
        }

        public void PlayCrownAward()
        {
            crownAwardEffectObject.SetActive(true);
            audioClient.Play(AudioKeys.CrownDance, transform, destroyCancellationToken);
            destroyCancellationToken.Register(() => audioClient.Stop(AudioKeys.CrownDance));
            UniTaskAsyncEnumerable.Timer(TimeSpan.FromSeconds(30)).Subscribe(_ =>
            {
                crownAwardEffectObject.SetActive(false);
                audioClient.Stop(AudioKeys.CrownDance);
            }).AddTo(destroyCancellationToken);
        }
    }
}