using System;
using System.Reactive;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Modules.UI;
using UnityEngine;

namespace Game.Views.UI.Screens.Menu
{
    public class SettingsPanel : MenuScreenPanelBase
    {
        [SerializeField] private ButtonWidget scaleUpWidget;
        [SerializeField] private ButtonWidget scaleDownWidget;
        [SerializeField] private ButtonWidget toggleVisibilityWidget;

        private readonly ISubject<Unit> onScaleUp = new Subject<Unit>();
        private readonly ISubject<Unit> onScaleDown = new Subject<Unit>();
        private readonly ISubject<Unit> onToggleVisibility = new Subject<Unit>();

        public IObservable<Unit> OnScaleUp => onScaleUp;
        public IObservable<Unit> OnScaleDown => onScaleDown;
        public IObservable<Unit> OnToggleVisibility => onToggleVisibility;

        protected override void Awake()
        {
            base.Awake();
            scaleUpWidget.OnClicked.Subscribe(_ => {
                onScaleUp.OnNext(Unit.Default);
            }).AddTo(destroyCancellationToken);

            scaleDownWidget.OnClicked.Subscribe(_ => {
                onScaleDown.OnNext(Unit.Default);
            }).AddTo(destroyCancellationToken);
            
            toggleVisibilityWidget.OnClicked.Subscribe(_ => {
                onToggleVisibility.OnNext(Unit.Default);
            }).AddTo(destroyCancellationToken);
        }

        public void SetActiveScaleUpButton(bool isActive)
        {
            scaleUpWidget.SetActive(isActive);
        }

        public void SetActiveScaleDownButton(bool isActive)
        {
            scaleDownWidget.SetActive(isActive);
        }
    }
}
